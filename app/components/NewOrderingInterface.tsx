import React, { useEffect, useReducer, useState, useMemo, useRef, useCallback } from "react";
import { AlertCircle, ArrowLeft, Check, ChevronsUpDown, ChevronLeft, ChevronRight, ChevronDown, Clock, Copy, Edit2, FileDown, Filter, Info, Loader2, Minus, MinusCircle, Plus, PlusCircle, Receipt, Search, Send, ShoppingCart, Trash2, Users, UserRound, MoreVertical, ChefHat, CheckSquare, XCircle, CheckCircle, RefreshCw, Printer, X, Beaker, Pizza, Utensils, Package, Truck } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent,  CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useMenuV4 } from "@/lib/hooks/use-menu-v4";
import { useSupplements } from "@/lib/hooks/useSupplements";
import { useTableDB } from "@/lib/hooks/useTableDB";
import { useOrderV4 } from "@/lib/hooks/use-order-v4";
import { useStaffMenuV4 } from "@/lib/hooks/useStaffMenuV4";
import { MenuItem } from "@/lib/db/v4/schemas/menu-schema";
import { OrderItem, PizzaQuarter } from "@/lib/db/v4/schemas/order-schema";
import { OrderAddon } from "@/lib/db/v4/schemas/order-schema";

import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { useOrderFinance } from '@/lib/hooks/use-order-finance';
import type { Order } from "@/lib/db/v4/schemas/order-schema";
import { useEditOrder } from '@/components/providers/EditOrderContext';
import { PRESET_COLORS } from "@/lib/constants";
import { DeliveryDriverSelector } from './DeliveryDriverSelector';
import { DeliveryPerson } from '@/lib/db/v4/schemas/order-schema';
import { PrintPreviewDialog } from './print/PrintPreviewDialog'; // Updated import
import AllPrintPreview from './print/AllPrintPreview';
import { OrderType, getOrderTypeLabel, requiresTable, requiresCustomerInfo } from '@/lib/types/order-types';
import { format } from 'date-fns';
import { staffService } from '@/lib/services/staff-service';
import StaffMenuManager from '@/components/staff/StaffMenuManager';


// Color generation utility no longer needed - removed

// Function to get color for menu items based strictly on category color from DB
const getItemColor = (categoryId: string, itemId: string, categoriesArray?: any[]): string => {
  if (categoriesArray && categoriesArray.length) {
    const category = categoriesArray.find(cat => cat.id === categoryId);
    if (category && category.items) {
      const item = category.items.find((item: any) => item.id === itemId);
      if (item && item.color) {
        return item.color; // Use the individual item's color from DB
      }
    }
  }
  // If no color in DB, use a neutral fallback (no palette, no generation)
  return '#e0e0e0';
};

// Add a function to get a lighter version of the same color for backgrounds
const getLightColor = (color: string): string => {
  return `${color}15`; // 15% opacity version of the same color
};

type NewOrder = Omit<Order, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt' | 'id'>;

// Define our reducer state type
interface OrderState {
  _id: string;
  id: string;
  type: string;
  orderType: OrderType;
  status: string;
  tableId: string;
  items: OrderItem[];
  total: number;
  createdAt: string;
  updatedAt: string;
  schemaVersion: string;
  notes: string;
  customer?: {
    name: string;
    phone: string;
    address?: string;
  };
  deliveryPerson?: DeliveryPerson;
  paymentStatus?: string;
}

// Define our UI state type (for temporary selections during item customization)
interface UiState {
  selectedCategory: string;
  selectedItemForAddons: string | null; // Format: "itemId-size" for size-specific selection
  selectedItemSizes: {[key: string]: string};
  itemNotes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  lastAddedItem: string | null;
}

// Define possible actions for our reducer
type OrderAction = 
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'LOAD_ORDER', payload: any }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' } }
  | { type: 'UPDATE_ITEM', payload: { itemId: string, updates: Partial<OrderItem> } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_ORDER_TYPE', payload: { orderType: OrderType } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_NOTES', payload: { notes: string } }
  | { type: 'SET_CUSTOMER_INFO', payload: { name: string, phone: string, address?: string } }
  | { type: 'SET_DELIVERY_INFO', payload: DeliveryPerson }
  | { type: 'RESET_ORDER' };

// Helper functions for the reducer
const getItemSignature = (item: OrderItem): string => [
  item.menuItemId || '',
  item.size || '',
  ((item.addons || []).map((a: OrderAddon) => a?.id || '').filter(Boolean).sort().join(',')),
  ((item.notes || '').trim())
].join('|');

const calculateTotal = (items: OrderItem[]): number => {
  const combinedItemsMap: { [key: string]: OrderItem } = {};
  items.forEach(item => {
    const itemSignature = getItemSignature(item);
    if (combinedItemsMap[itemSignature]) combinedItemsMap[itemSignature].quantity += item.quantity;
    else combinedItemsMap[itemSignature] = { ...item };
  });
  return Object.values(combinedItemsMap).reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const addonTotal = (item.addons || []).reduce((sum, addon) => sum + (addon.price || 0), 0) * item.quantity;
    return total + itemTotal + addonTotal;
  }, 0);
};

// Initialize order reducer state
const initialOrderState: OrderState = {
  _id: "",
  id: "",
  type: "order_document",
  orderType: "dine-in" as OrderType,
  status: "pending",
  tableId: "",
  items: [],
  total: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  schemaVersion: "v4.0",
  notes: ""
};

// Initialize UI state
const initialUiState: UiState = {
  selectedCategory: "",
  selectedItemForAddons: null,
  selectedItemSizes: {},
  itemNotes: {},
  selectedAddons: {},
  lastAddedItem: null
};

// Reducer function
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return { ...initialOrderState };
      
    case 'LOAD_ORDER':
      return { ...action.payload };
      
    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId // 🎯 Add categoryId to order item
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'ADD_CUSTOM_PIZZA': {
      const { quarters, size, notes, categoryId, pricingMethod } = action.payload;
      const uniqueId = `custom_pizza_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      // Calculate price based on pricing method
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      }
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: 'custom_pizza',
        name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
        size: size,
        price: price,
        quantity: 1,
        addons: [],
        notes: notes,
        categoryId: categoryId,
        compositeType: 'pizza_quarters',
        quarters: quarters
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'UPDATE_ITEM': {
      const { itemId, updates } = action.payload;
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, ...updates } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'INCREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, quantity: item.quantity + 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'DECREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const targetItem = state.items.find(item => item.id === itemId);
      
      if (!targetItem || targetItem.quantity <= 1) {
        const updatedItems = state.items.filter(item => item.id !== itemId);
        return {
          ...state,
          items: updatedItems,
          total: calculateTotal(updatedItems)
        };
      }
      
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, quantity: item.quantity - 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'REMOVE_ITEM': {
      const { itemId } = action.payload;
      const updatedItems = state.items.filter(item => item.id !== itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'SET_ORDER_TYPE': {
      return {
        ...state,
        orderType: action.payload.orderType
      };
    }
      
    case 'SET_TABLE': {
      return {
        ...state,
        tableId: action.payload.tableId
      };
    }
      
    case 'SET_NOTES': {
      return {
        ...state,
        notes: action.payload.notes
      };
    }
      
    case 'SET_CUSTOMER_INFO': {
      return {
        ...state,
        customer: {
          name: action.payload.name,
          phone: action.payload.phone,
          address: action.payload.address
        }
      };
    }
      
    case 'SET_DELIVERY_INFO': {
      return {
        ...state,
        deliveryPerson: action.payload
      };
    }
      
    case 'RESET_ORDER': {
      return { ...initialOrderState };
    }
      
    default:
      return state;
  }
};

// Small components that we'll use in our main interface

// CategorySelector Component
interface CategorySelectorProps {
  categories: any[];
  selectedCategory: string;
  onSelectCategory: (categoryId: string) => void;
}

const CategorySelector: React.FC<CategorySelectorProps> = React.memo(({ 
  categories, 
  selectedCategory, 
  onSelectCategory 
}) => {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-1.5">
      {categories.map((category) => {
        const isActive = selectedCategory === category.id;
        return (
          <div 
            key={category.id} 
            className={`rounded-md p-1 cursor-pointer transition-all duration-200 border flex flex-col items-center justify-center gap-0.5 hover:border-primary/70 hover:bg-primary/5 min-h-[60px] ${isActive ? 'border-primary bg-primary/10 shadow-sm' : 'border-muted bg-background'}`} 
            onClick={() => onSelectCategory(category.id || '')}
          >
            <div className={`text-xl ${isActive ? 'text-primary' : 'text-muted-foreground'}`}>
              {category.emoji || '🍽️'}
            </div>
            <div className={`font-medium text-center text-xs leading-none truncate w-full ${isActive ? 'text-primary' : 'text-foreground'}`}>
              {category.name}
            </div>
          </div>
        );
      })}
    </div>
  );
});

// MenuItemCard Component 
interface MenuItemCardProps {
  item: MenuItem;
  categoryId: string;
  categories: any[];
  isSelected: boolean;
  selectedSize: string | undefined;
  lastAddedItem: string | null;
  onSelect: (itemId: string) => void;
  onAddItem: (item: MenuItem, size: string) => void | Promise<void>;
}

const MenuItemCard: React.FC<MenuItemCardProps> = React.memo(({
  item,
  categoryId,
  categories,
  isSelected,
  selectedSize,
  lastAddedItem,
  onSelect,
  onAddItem
}) => {
  // Handler function to properly handle size button clicks
  const handleSizeButtonClick = useCallback((e: React.MouseEvent, size: string) => {
    e.stopPropagation(); // Stop event from bubbling up to parent
    onAddItem(item, size);
  }, [item, onAddItem]);

  const hasMultipleSizes = Object.keys(item.prices).length > 1;

  // Calculate if this is the only size
  const isOnlySize = Object.keys(item.prices).length === 1;
  
  // Generate color based on item and category
  const itemColor = getItemColor(categoryId, item.id, categories); // Pass categories

  return (
    <div
      className={cn(
        "p-2 transition-colors border rounded-lg flex flex-col justify-between h-auto",
        isSelected ? "ring-2 ring-primary/40" : "border-muted"
      )}
      style={{ 
        backgroundColor: itemColor
      }}
      // 🎯 Card click removed - only size buttons should be clickable
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="font-semibold text-sm truncate">{item.name}</div>
        </div>
      </div>
      <div className="mt-1 flex flex-wrap gap-1.5">
        {Object.entries(item.prices).map(([size, price]) => {
          const isItemSizeSelected = isSelected && selectedSize === size;
          const isJustAdded = lastAddedItem === `${item.id}-${size}`;
          
          return (
            <Button 
              key={`${item.id}-${size}`} 
              variant={isItemSizeSelected ? "default" : "outline"} 
              size="sm" 
              className={`h-8 py-0.5 text-xs rounded-md transition-all duration-300 flex-grow ${isOnlySize ? 'w-full' : 'min-w-[calc(50%-0.75rem)]'} ${isItemSizeSelected ? "bg-gray-900 text-white border-gray-900" : "bg-white text-gray-700 hover:bg-gray-100 border-gray-200"} ${isJustAdded ? 'shadow-sm border-green-500 border-2 bg-green-50 text-green-700' : ''}`} 
              onClick={(e) => handleSizeButtonClick(e, size)}
              type="button"
            >
              <div className="flex justify-between w-full items-center">
                <span>{size === "default" ? "Classique" : size}</span>
                {/* Show price for all menu items - supplements are no longer menu items */}
                <span className={`font-medium tabular-nums ${isItemSizeSelected ? "text-white" : isJustAdded ? "text-green-700" : "text-primary"}`}>
                  {price as number} DA
                </span>
              </div>
            </Button>
          );
        })}
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to optimize rerenders
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.selectedSize === nextProps.selectedSize &&
    prevProps.lastAddedItem === nextProps.lastAddedItem
  );
});

// Set display name for better debugging
MenuItemCard.displayName = 'MenuItemCard';

// More subcomponents we'll use in our UI

// ItemCustomizationPanel Component
interface ItemCustomizationPanelProps {
  category: any;
  item: MenuItem;
  selectedSize: string;
  selectedAddons: {[key: string]: Set<string>};
  itemNotes: {[key: string]: string};
  getAddonKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  isAddonSelected: (itemId: string, size: string, addonId: string) => boolean;
  toggleAddonSelection: (itemId: string, size: string, addonId: string) => void;
  updateItemNote: (itemId: string, size: string, note: string) => void;
  finalizeItem: () => void;
  categories: any[];
  // 🍕 Custom Pizza Props
  onCustomPizzaConfirm?: (quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average') => void;
  // ❌ Close function
  onClose?: () => void;
}

const ItemCustomizationPanel: React.FC<ItemCustomizationPanelProps> = React.memo(({
  category,
  item,
  selectedSize,
  selectedAddons,
  itemNotes,
  getAddonKey,
  getItemNoteKey,
  isAddonSelected,
  toggleAddonSelection,
  updateItemNote,
  finalizeItem,
  categories,
  onCustomPizzaConfirm,
  onClose
}) => {
  // Get the color for this item from its category
  const itemColor = item.color || category.color || '#e0e0e0';
  const lightColor = getLightColor(itemColor);
  
  // Get supplements from the category-specific supplements hook
  const { supplements } = useSupplements(category.id);
  
  // Filter to get only active supplements for this category
  const activeSupplements = supplements.filter(supplement => supplement.isActive !== false);
  
  // Convert supplements to addon format for UI consistency
  const allAddons = activeSupplements.map((supplement) => ({
    id: supplement.id,
    name: supplement.name,
    price: 0, // Supplements use global pricing, not item-specific pricing
    type: 'supplement' as const,
    originalSupplement: supplement, // Keep reference to original supplement
    stockConsumption: supplement.stockConsumption
  }));

  // 🍕 Custom Pizza State
  const [showPartitionSelection, setShowPartitionSelection] = useState(true);
  const [pizzaPartition, setPizzaPartition] = useState<'half' | 'quarter' | null>(null);
  const [pizzaSelections, setPizzaSelections] = useState<{
    half: { left: MenuItem | null; right: MenuItem | null };
    quarter: { topLeft: MenuItem | null; topRight: MenuItem | null; bottomLeft: MenuItem | null; bottomRight: MenuItem | null };
  }>({
    half: { left: null, right: null },
    quarter: { topLeft: null, topRight: null, bottomLeft: null, bottomRight: null }
  });
  
  // Get pizza items from the current category for selection
  const pizzaItems = category.items || [];

  const handleToggleAddon = (addonId: string) => {
    toggleAddonSelection(item.id, selectedSize, addonId);
  };

  const handleItemNote = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateItemNote(item.id, selectedSize, e.target.value);
  };

  // Handle panel close - should remove the last added item
  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      finalizeItem();
    }
  };

  // 🍕 Handle pizza selection for different partitions
  const handlePizzaSelect = (position: string, selectedPizza: MenuItem | null) => {
    if (!pizzaPartition) return;
    setPizzaSelections(prev => ({
      ...prev,
      [pizzaPartition]: {
        ...prev[pizzaPartition],
        [position]: selectedPizza
      }
    }));
  };

  // 🍕 Get current pizza selections based on partition type
  const getCurrentPizzas = () => {
    if (pizzaPartition === 'half') {
      return Object.values(pizzaSelections.half).filter(p => p !== null) as MenuItem[];
    } else if (pizzaPartition === 'quarter') {
      return Object.values(pizzaSelections.quarter).filter(p => p !== null) as MenuItem[];
    }
    return [];
  };

  // 🍕 Handle partition selection
  const handlePartitionSelect = (type: 'half' | 'quarter') => {
    setPizzaPartition(type);
    setShowPartitionSelection(false);
  };

  // 🍕 Handle back to partition selection
  const handleBackToPartitionSelection = () => {
    setShowPartitionSelection(true);
    setPizzaPartition(null);
  };

  // 🍕 Calculate custom pizza price
  const calculateCustomPizzaPrice = () => {
    const validPizzas = getCurrentPizzas();
    if (validPizzas.length === 0) return 0;
    
    const prices = validPizzas.map(pizza => pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0);
    
    if (category.quarterPricingMethod === 'average') {
      return prices.reduce((sum, price) => sum + price, 0) / prices.length;
    } else {
      return Math.max(...prices); // max pricing (default)
    }
  };

  // 🍕 Generate custom pizza name
  const generateCustomPizzaName = () => {
    const validPizzas = getCurrentPizzas();
    if (validPizzas.length === 0) return "Pizza Personnalisée";
    if (validPizzas.length === 1) return validPizzas[0].name;
    
    const uniqueNames = [...new Set(validPizzas.map(p => p.name))];
    if (uniqueNames.length === 1) return uniqueNames[0];
    if (uniqueNames.length === 2) return `${uniqueNames[0]} / ${uniqueNames[1]}`;
    
    return `Pizza Mixte (${uniqueNames.length} types)`;
  };

  // 🍕 Confirm custom pizza creation
  const handleConfirmCustomPizza = () => {
    const validPizzas = getCurrentPizzas();
    if (validPizzas.length === 0) return;
    
    // Convert to PizzaQuarter format - create quarters based on partition type
    let pizzaQuarterData: PizzaQuarter[];
    
    if (pizzaPartition === 'half') {
      const { left, right } = pizzaSelections.half;
      pizzaQuarterData = [];
      
      // Left half gets quarters 0 and 2, right half gets quarters 1 and 3
      if (left) {
        pizzaQuarterData.push(
          {
            menuItemId: left.id,
            name: left.name,
            price: left.prices[selectedSize] || Object.values(left.prices)[0] || 0,
            size: selectedSize
          },
          {
            menuItemId: left.id,
            name: left.name,
            price: left.prices[selectedSize] || Object.values(left.prices)[0] || 0,
            size: selectedSize
          }
        );
      }
      if (right) {
        pizzaQuarterData.push(
          {
            menuItemId: right.id,
            name: right.name,
            price: right.prices[selectedSize] || Object.values(right.prices)[0] || 0,
            size: selectedSize
          },
          {
            menuItemId: right.id,
            name: right.name,
            price: right.prices[selectedSize] || Object.values(right.prices)[0] || 0,
            size: selectedSize
          }
        );
      }
    } else {
      // Quarter partition
      const { topLeft, topRight, bottomLeft, bottomRight } = pizzaSelections.quarter;
      pizzaQuarterData = [topLeft, topRight, bottomLeft, bottomRight]
        .filter(p => p !== null)
        .map(pizza => ({
          menuItemId: pizza!.id,
          name: pizza!.name,
          price: pizza!.prices[selectedSize] || Object.values(pizza!.prices)[0] || 0,
          size: selectedSize
        }));
    }
    
    // Call the parent callback to add to order
    if (onCustomPizzaConfirm) {
      onCustomPizzaConfirm(
        pizzaQuarterData,
        selectedSize,
        itemNotes[getItemNoteKey(item.id, selectedSize)] || '',
        category.id,
        category.quarterPricingMethod || 'max'
      );
    }
    
    // Reset state
    setShowPartitionSelection(true);
    setPizzaPartition(null);
    setPizzaSelections({
      half: { left: null, right: null },
      quarter: { topLeft: null, topRight: null, bottomLeft: null, bottomRight: null }
    });
  };

  return (
    <div className="absolute bottom-0 left-0 right-0 bg-background border-t border-border shadow-lg max-h-[60vh] flex flex-col z-50">
      {/* 🎯 Compact Header with Notes Inline */}
      <div className="px-4 py-3 border-b border-border bg-muted/30">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <div className="flex items-center gap-2 min-w-0">
              <h3 className="font-semibold text-foreground truncate">{item.name}</h3>
              {selectedSize && selectedSize !== 'default' && (
                <Badge variant="secondary" className="text-xs flex-shrink-0">
                  {selectedSize}
                </Badge>
              )}
            </div>
            <Input
              placeholder="Instructions spéciales..."
              value={itemNotes[getItemNoteKey(item.id, selectedSize)] || ''}
              onChange={handleItemNote}
              className="h-8 text-sm flex-1 max-w-xs bg-background"
            />
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="h-8 w-8 p-0 hover:bg-destructive hover:text-destructive-foreground"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        <div className="p-4">
          {/* 🔄 Side by Side Layout: Custom Pizza | Addons */}
          <div className="flex gap-4">
            
            {/* 🍕 Custom Pizza Section - Left Side */}
            {category.isQuarterable && (
              <div className="flex-1 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Pizza className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-medium text-orange-800">🍕 Pizza Personnalisée</span>
                  </div>
                  {!showPartitionSelection && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={handleBackToPartitionSelection}
                      className="h-6 w-6 p-0 text-orange-600 hover:bg-orange-100"
                    >
                      <ArrowLeft className="h-3 w-3" />
                    </Button>
                  )}
                </div>
                
                <div className="space-y-3">
                  {/* Partition Selection or Selected View */}
                  {showPartitionSelection ? (
                    <div className="space-y-2">
                      <Label className="text-xs font-medium text-orange-700">
                        Choisir partition:
                      </Label>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handlePartitionSelect('half')}
                          className="flex-1 h-8 text-xs border-orange-200 text-orange-700 hover:bg-orange-50"
                        >
                          1/2 Moitié
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handlePartitionSelect('quarter')}
                          className="flex-1 h-8 text-xs border-orange-200 text-orange-700 hover:bg-orange-50"
                        >
                          1/4 Quarts
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <>
                      {/* Half Partition Layout */}
                      {pizzaPartition === 'half' && (
                        <div className="grid grid-cols-2 gap-2">
                          <div className="space-y-1">
                            <Label className="text-xs text-orange-700 font-medium">Gauche</Label>
                            <Select
                              value={pizzaSelections.half.left?.id || 'none'}
                              onValueChange={(value) => {
                                const selectedPizza = value === 'none' ? null : pizzaItems.find(p => p.id === value) || null;
                                handlePizzaSelect('left', selectedPizza);
                              }}
                            >
                              <SelectTrigger className="h-8 text-xs border-orange-300 focus:border-orange-500">
                                <SelectValue placeholder="Choisir pizza" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Aucune</SelectItem>
                                {pizzaItems.map((pizza: MenuItem) => (
                                  <SelectItem key={pizza.id} value={pizza.id}>
                                    {pizza.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-1">
                            <Label className="text-xs text-orange-700 font-medium">Droite</Label>
                            <Select
                              value={pizzaSelections.half.right?.id || 'none'}
                              onValueChange={(value) => {
                                const selectedPizza = value === 'none' ? null : pizzaItems.find(p => p.id === value) || null;
                                handlePizzaSelect('right', selectedPizza);
                              }}
                            >
                              <SelectTrigger className="h-8 text-xs border-orange-300 focus:border-orange-500">
                                <SelectValue placeholder="Choisir pizza" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Aucune</SelectItem>
                                {pizzaItems.map((pizza: MenuItem) => (
                                  <SelectItem key={pizza.id} value={pizza.id}>
                                    {pizza.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      )}
                      
                      {/* Quarter Partition Layout */}
                      {pizzaPartition === 'quarter' && (
                        <div className="grid grid-cols-2 gap-2">
                          <div className="space-y-1">
                            <Label className="text-xs text-orange-700 font-medium">Haut Gauche</Label>
                            <Select
                              value={pizzaSelections.quarter.topLeft?.id || 'none'}
                              onValueChange={(value) => {
                                const selectedPizza = value === 'none' ? null : pizzaItems.find(p => p.id === value) || null;
                                handlePizzaSelect('topLeft', selectedPizza);
                              }}
                            >
                              <SelectTrigger className="h-8 text-xs border-orange-300 focus:border-orange-500">
                                <SelectValue placeholder="Choisir" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Aucune</SelectItem>
                                {pizzaItems.map((pizza: MenuItem) => (
                                  <SelectItem key={pizza.id} value={pizza.id}>
                                    {pizza.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-1">
                            <Label className="text-xs text-orange-700 font-medium">Haut Droite</Label>
                            <Select
                              value={pizzaSelections.quarter.topRight?.id || 'none'}
                              onValueChange={(value) => {
                                const selectedPizza = value === 'none' ? null : pizzaItems.find(p => p.id === value) || null;
                                handlePizzaSelect('topRight', selectedPizza);
                              }}
                            >
                              <SelectTrigger className="h-8 text-xs border-orange-300 focus:border-orange-500">
                                <SelectValue placeholder="Choisir" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Aucune</SelectItem>
                                {pizzaItems.map((pizza: MenuItem) => (
                                  <SelectItem key={pizza.id} value={pizza.id}>
                                    {pizza.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                                                    <div className="space-y-1">
                            <Label className="text-xs text-orange-700 font-medium">Bas Gauche</Label>
                            <Select
                              value={pizzaSelections.quarter.bottomLeft?.id || 'none'}
                              onValueChange={(value) => {
                                const selectedPizza = value === 'none' ? null : pizzaItems.find(p => p.id === value) || null;
                                handlePizzaSelect('bottomLeft', selectedPizza);
                              }}
                            >
                              <SelectTrigger className="h-8 text-xs border-orange-300 focus:border-orange-500">
                                <SelectValue placeholder="Choisir" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Aucune</SelectItem>
                                {pizzaItems.map((pizza: MenuItem) => (
                                  <SelectItem key={pizza.id} value={pizza.id}>
                                    {pizza.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-1">
                            <Label className="text-xs text-orange-700 font-medium">Bas Droite</Label>
                            <Select
                              value={pizzaSelections.quarter.bottomRight?.id || 'none'}
                              onValueChange={(value) => {
                                const selectedPizza = value === 'none' ? null : pizzaItems.find(p => p.id === value) || null;
                                handlePizzaSelect('bottomRight', selectedPizza);
                              }}
                            >
                              <SelectTrigger className="h-8 text-xs border-orange-300 focus:border-orange-500">
                                <SelectValue placeholder="Choisir" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Aucune</SelectItem>
                                {pizzaItems.map((pizza: MenuItem) => (
                                  <SelectItem key={pizza.id} value={pizza.id}>
                                    {pizza.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      )}
                      
                      {/* Pizza Summary */}
                      {getCurrentPizzas().length > 0 && (
                        <div className="p-2 bg-white border border-orange-300 rounded text-xs">
                          <div className="font-medium text-orange-800 mb-1">{generateCustomPizzaName()}</div>
                          <div className="flex justify-between text-orange-600">
                            <span>Prix ({category.quarterPricingMethod === 'average' ? 'Moyen' : 'Maximum'}):</span>
                            <span className="font-semibold">{calculateCustomPizzaPrice().toFixed(0)} DA</span>
                          </div>
                        </div>
                      )}
                      
                      {/* Action Buttons */}
                      <div className="flex gap-2">
                        <Button
                          onClick={() => setPizzaSelections({
                            half: { left: null, right: null },
                            quarter: { topLeft: null, topRight: null, bottomLeft: null, bottomRight: null }
                          })}
                          variant="outline"
                          size="sm"
                          className="flex-1 text-xs h-8 border-orange-300 text-orange-700 hover:bg-orange-100"
                        >
                          Effacer
                        </Button>
                        <Button
                          onClick={handleConfirmCustomPizza}
                          disabled={getCurrentPizzas().length === 0}
                          size="sm"
                          className="flex-1 text-xs h-8 bg-orange-600 hover:bg-orange-700 text-white disabled:opacity-50"
                        >
                          Ajouter
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
            
            {/* 🧹 Clean Supplements Section - Right Side */}
            {allAddons.length > 0 && (
              <div className={`${category.isQuarterable ? 'flex-1' : 'w-full'}`}>
                <div className="flex items-center gap-2 mb-3">
                  <Beaker className="h-4 w-4 text-muted-foreground" />
                  <h4 className="text-sm font-medium text-foreground">Suppléments Disponibles</h4>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  {allAddons.map((addon) => {
                    const isSelected = isAddonSelected(item.id, selectedSize, addon.id);
                    return (
                      <Button
                        key={`${item.id}-supplement-${addon.id}`}
                        variant={isSelected ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleToggleAddon(addon.id)}
                        className={`h-10 justify-start text-left p-2 transition-all duration-200 ${
                          isSelected 
                            ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
                            : 'bg-background hover:bg-muted/50 border-border'
                        }`}
                      >
                        <div className="flex items-center gap-2 w-full">
                          <div className={`w-4 h-4 rounded-sm flex items-center justify-center transition-colors border flex-shrink-0 ${
                            isSelected 
                              ? 'bg-primary-foreground text-primary border-primary-foreground' 
                              : 'bg-background border-border'
                          }`}>
                            {isSelected && <Check className="h-3 w-3" />}
                          </div>
                          <span className="text-sm font-medium truncate">{addon.name}</span>
                        </div>
                      </Button>
                    );
                  })}
                </div>
              </div>
            )}
            
            {/* 🧹 Full Width Supplements when no custom pizza */}
            {!category.isQuarterable && allAddons.length === 0 && (
              <div className="flex-1 flex items-center justify-center text-muted-foreground text-sm">
                Aucun supplément disponible
              </div>
            )}
            
          </div>
        </div>
      </div>
    </div>
  );
}, (prevProps: ItemCustomizationPanelProps, nextProps: ItemCustomizationPanelProps) => {
  // Only re-render if these specific props change
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.selectedSize === nextProps.selectedSize &&
    prevProps.itemNotes[prevProps.getItemNoteKey(prevProps.item.id, prevProps.selectedSize)] === 
      nextProps.itemNotes[nextProps.getItemNoteKey(nextProps.item.id, nextProps.selectedSize)] &&
    JSON.stringify(Array.from(prevProps.selectedAddons[prevProps.getAddonKey(prevProps.item.id, prevProps.selectedSize)] || new Set())) === 
      JSON.stringify(Array.from(nextProps.selectedAddons[nextProps.getAddonKey(nextProps.item.id, nextProps.selectedSize)] || new Set()))
  );
});

// Add extended OrderItem type to include the combined item IDs
interface CombinedOrderItem extends OrderItem {
  originalId?: string;
  allIds?: string[];
}

// OrderSummary Component
interface OrderSummaryProps {
  items: OrderItem[];
  total: number;
  onIncrement: (itemId: string) => void;
  onDecrement: (itemId: string) => void;
  onRemove: (itemId: string) => void;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  setSelectedItemForAddons: (itemId: string | null) => void;
  setSelectedItemSizes: (updater: (prev: {[key: string]: string}) => {[key: string]: string}) => void;
  setSelectedAddons: (updater: (prev: {[key: string]: Set<string>}) => {[key: string]: Set<string>}) => void;
  setItemNotes: (updater: (prev: {[key: string]: string}) => {[key: string]: string}) => void;
  getAddonKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  displayCategories: any[];
}

const OrderSummary: React.FC<OrderSummaryProps> = React.memo(({
  items,
  total,
  onIncrement,
  onDecrement,
  onRemove,
  selectedItemForAddons,
  selectedItemSizes,
  setSelectedItemForAddons,
  setSelectedItemSizes,
  setSelectedAddons,
  setItemNotes,
  getAddonKey,
  getItemNoteKey,
  displayCategories
}) => {
  // Track which groups are collapsed - store in state that persists across renders
  const [collapsedGroups, setCollapsedGroups] = useState<{[key: string]: boolean}>({});

  // Toggle collapsed state for a group
  const toggleGroupCollapse = useCallback((groupId: string) => {
    setCollapsedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  }, []);

  // Function to get category ID for menu items - needed for grouping
  const getCategoryIdForItem = useCallback((itemId: string): string => {
    // Find the category that contains this item
    const category = displayCategories.find(cat => 
      cat.items.some((item: MenuItem) => item.id === itemId)
    );
    return category?.id || 'unknown';
  }, [displayCategories]);

  // Create a map to combine identical items (same item, size, addons, notes)
  const groupedItems = useMemo(() => {
    // First combine items with identical properties
    const combinedItemsMap: { [key: string]: OrderItem } = {};
    const combinedItemIds: { [key: string]: string[] } = {}; // Track all IDs for items with same signature
    
    items.forEach(item => {
      const itemSignature = getItemSignature(item);
      
      if (combinedItemsMap[itemSignature]) {
        // Add quantity to existing item
        combinedItemsMap[itemSignature].quantity += item.quantity;
        // Track the ID for later removal/increment operations
        combinedItemIds[itemSignature].push(item.id);
      } else {
        // Create new entry
        combinedItemsMap[itemSignature] = { ...item };
        combinedItemIds[itemSignature] = [item.id];
      }
    });
    
    // Create properly combined items with preserved first ID
    const combinedItems = Object.entries(combinedItemsMap).map(([signature, item]) => {
      return {
        ...item,
        // We'll keep using the first ID for UI operations
        originalId: item.id,
        allIds: combinedItemIds[signature]
      } as CombinedOrderItem;
    });
    
    // 🎯 Group by category instead of item name
    const categoryGroups: { [key: string]: CombinedOrderItem[] } = {};
    combinedItems.forEach(item => {
      const categoryId = getCategoryIdForItem(item.menuItemId);
      const category = displayCategories.find(cat => cat.id === categoryId);
      const groupKey = category?.name || 'Unknown Category'; // Group by category name
      if (!categoryGroups[groupKey]) categoryGroups[groupKey] = [];
      categoryGroups[groupKey].push(item);
    });
    
    // Sort items within each category group by item name first, then size, then addons, then notes
    Object.values(categoryGroups).forEach(groupItems => {
      groupItems.sort((a, b) => {
        // First sort by item name
        if (a.name !== b.name) {
          return a.name.localeCompare(b.name);
        }
        // Then by size
        if (a.size !== b.size) { 
          if (a.size === undefined) return -1; 
          if (b.size === undefined) return 1; 
          return a.size.localeCompare(b.size); 
        }
        // Then by number of addons
        if ((a.addons || []).length !== (b.addons || []).length) 
          return (a.addons || []).length - (b.addons || []).length;
        
        // Then by addon names
        const aAddonNames = (a.addons || []).map(addon => addon.name).sort().join(',');
        const bAddonNames = (b.addons || []).map(addon => addon.name).sort().join(',');
        if (aAddonNames !== bAddonNames) return aAddonNames.localeCompare(bAddonNames);
        
        // Finally by notes
        return (a.notes || '').localeCompare(b.notes || '');
      });
    });

    return categoryGroups;
  }, [items, getCategoryIdForItem, displayCategories]);

  // Memoize handler for selecting an item for customization
  const handleSelectItem = useCallback((menuItem: MenuItem, size: string, addons: OrderAddon[], notes: string) => {
    if (menuItem) {
      setSelectedItemForAddons(`${menuItem.id}-${size || 'default'}`);
      setSelectedItemSizes(prev => ({
        ...prev,
        [menuItem.id]: size || 'default'
      }));
      
      const addonKey = getAddonKey(menuItem.id, size || 'default');
      const addonSet = new Set(addons.map(addon => addon.id));
      setSelectedAddons(prev => ({ 
        ...prev, 
        [addonKey]: addonSet 
      }));
      
      const noteKey = getItemNoteKey(menuItem.id, size || 'default');
      setItemNotes(prev => ({ 
        ...prev, 
        [noteKey]: notes || '' 
      }));
    }
  }, [
    setSelectedItemForAddons, 
    setSelectedItemSizes, 
    setSelectedAddons, 
    setItemNotes, 
    getAddonKey, 
    getItemNoteKey
  ]);

  // Calculate total number of items across all groups
  const totalItemCount = useMemo(() => {
    return items.reduce((total, item) => total + item.quantity, 0);
  }, [items]);



  return (
    <div className="flex-grow overflow-hidden min-h-0 flex flex-col">
      <ScrollArea className="h-full">
        <div className="px-2 py-1 border-b bg-muted/5 flex items-center justify-between">
          <h3 className="font-medium text-sm">Résumé de la commande</h3>
          <div className="text-xs font-medium bg-primary/10 text-primary px-2 py-0.5 rounded-full">
            {totalItemCount} article(s)
          </div>
        </div>
        
        <div className="p-0.5 flex flex-col">
          {items.length === 0 ? (
            <div className="flex-grow flex flex-col items-center justify-center py-6 px-2 text-center">
              <div className="w-8 h-8 rounded-full bg-muted/50 flex items-center justify-center mb-1.5">
                <ShoppingCart className="h-3.5 w-3.5 text-muted-foreground" />
              </div>
              <div className="text-muted-foreground text-xs">Aucun article dans la commande</div>
              <div className="text-[10px] text-muted-foreground mt-1">Sélectionnez des articles dans le menu pour commencer</div>
            </div>
          ) : (
            <div className="flex-grow flex flex-col">
              {Object.entries(groupedItems).map(([categoryName, itemsInCategory]) => {
                const isCollapsed = collapsedGroups[categoryName] === true;

                return (
                  <div key={categoryName} className="border-b border-gray-100 last:border-b-0">
                    <div
                      className="flex items-center justify-between px-2.5 py-2 cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => toggleGroupCollapse(categoryName)}
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className={`transition-transform duration-200 ${isCollapsed ? '' : 'rotate-90'}`}
                        >
                          <ChevronRight className="h-4 w-4 text-gray-400" />
                        </div>
                        <div>
                          <div className="font-semibold text-sm truncate">{categoryName}</div>
                          <div className="text-xs text-muted-foreground">
                            {itemsInCategory.reduce((total, item) => total + item.quantity, 0)} article(s)
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="text-sm font-semibold text-right">
                          {itemsInCategory.reduce((total, item) => {
                            const itemBasePrice = item.price * item.quantity;
                            const itemAddonPrice = (item.addons || []).reduce((sum, addon) => sum + (addon.price || 0), 0) * item.quantity;
                            return total + itemBasePrice + itemAddonPrice;
                          }, 0)} DA
                        </div>
                      </div>
                    </div>

                    <div
                      className={`transition-all duration-300 ease-in-out ${isCollapsed ? 'max-h-0 overflow-hidden' : 'max-h-[1000px]'}`}
                    >
                      <div className="divide-y divide-gray-100">
                        {itemsInCategory.map((item: CombinedOrderItem) => {
                          const individualItemCategoryId = getCategoryIdForItem(item.menuItemId);
                          const individualItemColor = getItemColor(individualItemCategoryId, item.menuItemId, displayCategories);
                          const isSelectedForEditing = selectedItemForAddons === `${item.menuItemId}-${item.size || 'default'}`;

                          const handleItemClick = () => {
                            const menuCategory = displayCategories.find((c: any) =>
                              c.items.some((menuItem: MenuItem) => menuItem.id === item.menuItemId)
                            );

                            if (!menuCategory) return;

                            const menuItem = menuCategory.items.find((i: MenuItem) => i.id === item.menuItemId);
                            if (!menuItem) return;

                            setSelectedItemForAddons(`${item.menuItemId}-${item.size || 'default'}`);
                            setSelectedItemSizes(prev => ({ ...prev, [item.menuItemId]: item.size || "" }));

                            const addonKey = getAddonKey(item.menuItemId, item.size || "");
                            setSelectedAddons(prev => {
                              const newSelectedAddons = { ...prev };
                              newSelectedAddons[addonKey] = new Set(
                                (item.addons || []).map((addon: OrderAddon) => addon.id)
                              );
                              return newSelectedAddons;
                            });

                            if (item.notes) {
                              const noteKey = getItemNoteKey(item.menuItemId, item.size || "");
                              setItemNotes(prev => ({
                                ...prev,
                                [noteKey]: item.notes || ""
                              }));
                            }
                          };

                          return (
                            <div
                              key={item.id || `combined-${item.menuItemId}-${item.size}`}
                              className={cn(
                                "transition-colors",
                                isSelectedForEditing ? "bg-blue-50" : "bg-white"
                              )}
                              style={{
                                borderLeft: `3px solid ${isSelectedForEditing ? '#3b82f6' : individualItemColor}`
                              }}
                            >
                              <div className="flex items-center gap-2 px-2.5 py-1.5">
                                <div className="flex-1 py-0.5">
                                  <div
                                    className="flex items-center cursor-pointer"
                                    onClick={handleItemClick}
                                  >
                                    <div className="font-medium text-sm truncate flex-1">
                                      <span className="font-medium">{item.name}</span>
                                      {item.size && item.size !== 'default' && (
                                        <span className="text-muted-foreground ml-1.5 font-normal text-xs">
                                          ({item.size})
                                        </span>
                                      )}
                                    </div>
                                    <div className="text-sm font-medium mr-3">{item.price + (item.addons || []).reduce((sum, addon) => sum + (addon.price || 0), 0)} DA</div>
                                  </div>

                                  {((item.addons?.length || 0) > 0 || item.notes) && (
                                    <div className="text-xs text-muted-foreground mt-1 space-y-0.5 pl-1">
                                      {item.addons && item.addons.length > 0 && (
                                        <div className="flex flex-wrap gap-x-1 gap-y-1">
                                          {item.addons.map((addon: OrderAddon) => (
                                            <div 
                                              key={addon.id} 
                                              className="inline-flex items-center gap-1 px-1.5 py-0.5 rounded bg-gray-100 text-gray-600"
                                            >
                                              <span className="text-[11px]">{addon.name}</span>
                                              {addon.price > 0 && (
                                                <span className="font-semibold text-[11px]">+{addon.price}</span>
                                              )}
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                      {item.notes && <div className="italic text-gray-500 pt-1">{item.notes}</div>}
                                    </div>
                                  )}
                                </div>

                                <div className="flex items-center gap-1.5">
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    className="w-7 h-7"
                                    onClick={() => onDecrement(item.allIds?.[0] || item.id)}
                                  >
                                    <Minus className="h-3.5 w-3.5" />
                                  </Button>
                                  <div
                                    className="w-7 h-7 rounded-md flex items-center justify-center text-sm font-semibold bg-gray-100"
                                  >
                                    {item.quantity}
                                  </div>
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    className="w-7 h-7"
                                    onClick={() => onIncrement(item.allIds?.[0] || item.id)}
                                  >
                                    <Plus className="h-3.5 w-3.5" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
});

// Complex StaffMenuDetails component removed - will be replaced with clean StaffMenuManager integration

// OrderDetails Component for order type, table selection, and customer info
interface OrderDetailsProps {
  orderType: OrderType;
  tableId: string;
  tables: any[];
  tableStatuses: any[];
  customer: { name: string; phone: string; address?: string } | undefined;
  deliveryPerson: DeliveryPerson | undefined;
  notes: string;
  onOrderTypeChange: (orderType: OrderType) => void;
  onTableChange: (tableId: string) => void;
  onNotesChange: (notes: string) => void;
  onCustomerInfoChange: (field: string, value: string) => void;
  onDeliveryPersonChange: (deliveryPerson: DeliveryPerson) => void;
}

const OrderDetails: React.FC<OrderDetailsProps> = React.memo(({
  orderType,
  tableId,
  tables,
  tableStatuses,
  customer = { name: "", phone: "" },
  deliveryPerson,
  notes,
  onOrderTypeChange,
  onTableChange,
  onNotesChange,
  onCustomerInfoChange,
  onDeliveryPersonChange,
}) => {
  // Memoize handlers
  const handleNotesChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onNotesChange(e.target.value);
  }, [onNotesChange]);

  const handleCustomerChange = useCallback((field: string, e: React.ChangeEvent<HTMLInputElement>) => {
    onCustomerInfoChange(field, e.target.value);
  }, [onCustomerInfoChange]);

  // Memoize table options to prevent unnecessary recalculations
  const tableOptions = useMemo(() => {
    return tables.map((table) => {
      const tableStatus = tableStatuses.find(t => t.id === table.id);
      const isOccupied = tableStatus?.status === 'occupied';
      return {
        id: table.id,
        name: table.name || `Table ${table.seats}`,
        seats: table.seats,
        isOccupied,
        occupiedSince: tableStatus?.occupiedSince,
        disabled: isOccupied && table.id !== tableId
      };
    });
  }, [tables, tableStatuses, tableId]);

  return (
    <div className="flex-shrink-0 border-b bg-muted/10 p-1.5">
      <div className="flex gap-1 w-full mb-2">
        <Button 
          variant={orderType === 'dine-in' ? 'default' : 'outline'} 
          size="sm" 
          className="w-full gap-1 rounded-md h-8 py-0" 
          onClick={() => onOrderTypeChange('dine-in')}
        >
          <Utensils className="h-3 w-3 mr-1" />
          <span className="text-xs font-medium">{getOrderTypeLabel('dine-in')}</span>
        </Button>
        <Button 
          variant={orderType === 'takeaway' ? 'default' : 'outline'} 
          size="sm" 
          className="w-full gap-1 rounded-md h-8 py-0" 
          onClick={() => onOrderTypeChange('takeaway')}
        >
          <Package className="h-3 w-3 mr-1" />
          <span className="text-xs font-medium">{getOrderTypeLabel('takeaway')}</span>
        </Button>
        <Button 
          variant={orderType === 'delivery' ? 'default' : 'outline'} 
          size="sm" 
          className="w-full gap-1 rounded-md h-8 py-0" 
          onClick={() => onOrderTypeChange('delivery')}
        >
          <Truck className="h-3 w-3 mr-1" />
          <span className="text-xs font-medium">{getOrderTypeLabel('delivery')}</span>
        </Button>
      </div>
      <div className="bg-background rounded-md border p-1.5">
        {requiresTable(orderType) && (
          <div>
            <Select 
              value={tableId} 
              onValueChange={onTableChange} 
              aria-label="Select Table"
            >
              <SelectTrigger id="table" className="h-7 text-xs">
                <SelectValue placeholder="Sélectionner une table" />
              </SelectTrigger>
              <SelectContent>
                {tableOptions.map((table) => (
                  <SelectItem key={table.id} value={table.id} disabled={table.disabled}>
                    <div className="flex flex-col py-1">
                      <div className="flex items-center gap-2">
                        <div className={`w-2.5 h-2.5 rounded-full ${table.isOccupied ? 'bg-yellow-500' : 'bg-green-500'}`}></div>
                        <span className="font-medium text-sm">{table.name}</span>
                        <span className="text-sm text-muted-foreground">({table.seats} places)</span>
                      </div>
                      {table.isOccupied && table.occupiedSince && (
                        <div className="text-sm text-muted-foreground mt-1.5 flex items-center gap-1.5">
                          <Clock className="h-3.5 w-3.5" />
                          <span>Occupée depuis : </span>
                          <span className="font-mono">{(() => { 
                            const now = new Date(); 
                            const start = new Date(table.occupiedSince); 
                            const diff = now.getTime() - start.getTime(); 
                            const hours = Math.floor(diff / (1000 * 60 * 60)); 
                            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60)); 
                            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`; 
                          })()}</span>
                        </div>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        
        {requiresCustomerInfo(orderType) && (
          <div className="space-y-1.5">
            <div className="grid grid-cols-2 gap-1.5">
              <Input 
                placeholder="Nom du client" 
                className="h-7 text-sm" 
                value={customer?.name || ''} 
                onChange={(e) => handleCustomerChange('name', e)} 
              />
              <Input 
                placeholder="Téléphone *" 
                className="h-7 text-sm" 
                value={customer?.phone || ''} 
                onChange={(e) => handleCustomerChange('phone', e)} 
              />
            </div>
            
            {orderType === 'delivery' && (
              <>
                <Input 
                  placeholder="Adresse de livraison" 
                  className="h-7 text-sm" 
                  value={customer?.address || ''} 
                  onChange={(e) => handleCustomerChange('address', e)} 
                />
                <div className="pt-1.5 border-t">
                  <p className="text-sm font-medium mb-1">Livreur</p>
                  <DeliveryDriverSelector
                    deliveryPerson={deliveryPerson}
                    onDeliveryPersonChange={onDeliveryPersonChange}
                  />
                </div>
              </>
            )}
          </div>
        )}
        
        <div className="mt-1.5">
          <Textarea 
            placeholder="Notes pour la commande..." 
            className="resize-none h-[40px] text-xs" 
            value={notes} 
            onChange={handleNotesChange} 
          />
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Only re-render when these specific props change
  return (
    prevProps.orderType === nextProps.orderType &&
    prevProps.tableId === nextProps.tableId &&
    prevProps.notes === nextProps.notes &&
    prevProps.customer?.name === nextProps.customer?.name &&
    prevProps.customer?.phone === nextProps.customer?.phone &&
    prevProps.customer?.address === nextProps.customer?.address &&
    prevProps.deliveryPerson?.name === nextProps.deliveryPerson?.name &&
    prevProps.deliveryPerson?.phone === nextProps.deliveryPerson?.phone &&
    prevProps.tables.length === nextProps.tables.length &&
    prevProps.tableStatuses.length === nextProps.tableStatuses.length
  );
});

// Main component
interface OrderingInterfaceProps {}

const NewOrderingInterface: React.FC<OrderingInterfaceProps> = () => {
  // Main hooks
  const { isAuthenticated, user } = useAuth();
  const { categories, isLoading: menuLoading, error: menuError, isReady: menuReady } = useMenuV4();
  const { tables, isLoading: tablesLoading, error: tablesError, isReady: tablesReady } = useTableDB();
  const { orders, isLoading: ordersLoading, error: ordersError, isReady: ordersReady, createOrder, refreshOrders, getOrder } = useOrderV4();
  const { 
    config: staffMenuConfig, 
    staffMenuItems, 
    isLoading: staffMenuLoading,
    error: staffMenuError
  } = useStaffMenuV4();
  

  const { toast } = useToast();
  useOrderFinance(); // For side effects if needed
  const { editOrder, isEditMode, editSessionId, clearEditOrder, refreshEditSession } = useEditOrder();

  // Core state with reducer
  const [orderState, dispatch] = useReducer(orderReducer, initialOrderState);
  
  // UI State (for temporary states during item customization)
  const [uiState, setUiState] = useState<UiState>(initialUiState);
  

  
  // UI/UX state - SIMPLIFIED LOADING LOGIC
  const isLoading = menuLoading && tablesLoading && ordersLoading; // Only loading if ALL are loading
  const error = menuError || tablesError || ordersError;
  const isReady = (menuReady || (categories.length > 0 && tables.length >= 0)) && tablesReady; // More flexible ready state
  const [tableStatuses, setTableStatuses] = useState<{ id: string; status: 'free' | 'occupied'; occupiedSince: string | null; currentOrderId: string | null; }[]>([]);
  const [isOperationLoading, setIsOperationLoading] = useState(false);
  const [localCategories, setLocalCategories] = useState<any[]>([]);
  const popupRef = useRef<HTMLDivElement>(null);
  const [tabValue, setTabValue] = useState("ordering");
  // isEditMode now comes from useEditOrder context
  const [showPrintPreview, setShowPrintPreview] = useState(false);
  const [showAllPrintPreview, setShowAllPrintPreview] = useState(false);
  const [printJob, setPrintJob] = useState<{ title: string; content: string; type: 'kitchen' | 'receipt' | 'report' | 'expo' } | null>(null);
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const staffMenuRef = useRef<any>(null);
  const [isStaffMenuReady, setIsStaffMenuReady] = useState(false);


  // Add loading timeout to show manual refresh option
  useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        setLoadingTimeout(true);
      }, 8000); // Show timeout after 8 seconds
      
      return () => clearTimeout(timer);
    } else {
      setLoadingTimeout(false);
    }
  }, [isLoading]);

  // Reset staff menu ready state when switching away from staff menu
  useEffect(() => {
    if (uiState.selectedCategory !== 'staff-menu') {
      setIsStaffMenuReady(false);
    }
  }, [uiState.selectedCategory]);

  // Get current business day and time info
  const getCurrentBusinessInfo = () => {
    const now = new Date();
    const currentHour = now.getHours();
    
    // Business day reset at 5 AM (from memory)
    let businessDate;
    if (currentHour < 5) {
      // Before 5 AM = previous calendar day's business
      businessDate = new Date(now);
      businessDate.setDate(businessDate.getDate() - 1);
    } else {
      businessDate = now;
    }
    
    const businessDateStr = format(businessDate, 'yyyy-MM-dd');
    const dayName = format(businessDate, 'EEEE').toLowerCase();
    
    return { businessDateStr, dayName, currentTime: format(now, 'HH:mm') };
  };

  // --- UTILITY FUNCTIONS ---
  const getItemNoteKey = useCallback((itemId: string, size: string) => `note-${itemId}-${size}`, []);
  const getAddonKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);
  
  const isAddonSelected = useCallback((itemId: string, size: string, addonId: string) => {
    const key = getAddonKey(itemId, size);
    return uiState.selectedAddons[key]?.has(addonId) || false;
  }, [uiState.selectedAddons, getAddonKey]);
  
  // --- UI STATE UPDATERS ---
  const setSelectedCategory = useCallback((categoryId: string) => {
    setUiState(prev => ({ ...prev, selectedCategory: categoryId }));
  }, []);
  
  const setSelectedItemForAddons = useCallback((itemId: string | null) => {
    setUiState(prev => ({ ...prev, selectedItemForAddons: itemId }));
  }, []);
  
  const setLastAddedItem = useCallback((itemSignature: string | null) => {
    setUiState(prev => ({ ...prev, lastAddedItem: itemSignature }));
    if (itemSignature) {
      // Clear the "just added" highlight after a delay
      setTimeout(() => {
        setUiState(prev => ({ ...prev, lastAddedItem: null }));
      }, 800);
    }
  }, []);
  
  const initializeFreshItemState = useCallback((itemId: string, size: string) => {
    // Update size selection
    setUiState(prev => {
      // Create new references for nested objects to ensure React detects the changes
      const newAddons = { ...prev.selectedAddons };
      newAddons[getAddonKey(itemId, size)] = new Set();
      
      const newNotes = { ...prev.itemNotes };
      newNotes[getItemNoteKey(itemId, size)] = '';
      
      return {
        ...prev,
        selectedItemSizes: { ...prev.selectedItemSizes, [itemId]: size },
        selectedAddons: newAddons,
        itemNotes: newNotes
      };
    });
  }, [getAddonKey, getItemNoteKey]);
  
  const toggleAddonSelection = useCallback((itemId: string, size: string, addonId: string) => {
    const key = getAddonKey(itemId, size);
    
    setUiState(prev => {
      // Create a new reference for the selectedAddons object
      const newSelections = {...prev.selectedAddons};
      if (!newSelections[key]) newSelections[key] = new Set();
      
      // Create a new Set reference to ensure React detects the change
      const updatedSet = new Set(newSelections[key]);
      if (updatedSet.has(addonId)) {
        updatedSet.delete(addonId);
      } else {
        updatedSet.add(addonId);
      }
      
      newSelections[key] = updatedSet;
      return {
        ...prev,
        selectedAddons: newSelections
      };
    });
  }, [getAddonKey]);
  
  const updateItemNote = useCallback((itemId: string, size: string, note: string) => {
    const noteKey = getItemNoteKey(itemId, size);
    
    setUiState(prev => {
      // Create a new reference for the itemNotes object
      const newNotes = {...prev.itemNotes};
      newNotes[noteKey] = note;
      
      return {
        ...prev,
        itemNotes: newNotes
      };
    });
  }, [getItemNoteKey]);
  
  // Helper function to get supplements for a specific category
  const getCategorySupplements = useCallback(async (categoryId: string) => {
    try {
      const { getAllSupplements } = await import('@/lib/db/v4/operations/supplement-ops');
      return await getAllSupplements(categoryId);
    } catch (error) {
      console.error('Error getting category supplements:', error);
      return [];
    }
  }, []);

  // Helper function to get supplement prices for a specific category
  const getCategorySupplementPrices = useCallback(async (categoryId: string) => {
    try {
      const { getAllSupplementPrices } = await import('@/lib/db/v4/operations/supplement-ops');
      return await getAllSupplementPrices(categoryId);
    } catch (error) {
      console.error('Error getting category supplement prices:', error);
      return {};
    }
  }, []);
  
  // Memoize handlers for order manipulation
  const handleIncrement = useCallback((itemId: string) => {
    dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleDecrement = useCallback((itemId: string) => {
    dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleRemoveItem = useCallback((itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: { itemId } });
  }, []);

  // Staff menu handlers removed - will be replaced with clean StaffMenuManager integration

  const handleOrderTypeChange = useCallback((orderType: OrderType) => {
    dispatch({ type: 'SET_ORDER_TYPE', payload: { orderType } });
    
    // Clear table when switching away from dine-in
    if (orderType !== 'dine-in') {
      dispatch({ type: 'SET_TABLE', payload: { tableId: '' } });
    }
    
    // Clear customer info when switching to dine-in
    if (orderType === 'dine-in') {
      dispatch({ type: 'SET_CUSTOMER_INFO', payload: { name: '', phone: '', address: '' } });
    }
  }, []);

  const handleTableChange = useCallback((tableId: string) => {
    dispatch({ type: 'SET_TABLE', payload: { tableId } });
  }, []);

  const handleNotesChange = useCallback((notes: string) => {
    dispatch({ type: 'SET_NOTES', payload: { notes } });
  }, []);

  const handleCustomerInfoChange = useCallback((field: string, value: string) => {
    const customer = { ...(orderState.customer || { name: "", phone: "" }) };
    customer[field as keyof typeof customer] = value;
    dispatch({ 
      type: 'SET_CUSTOMER_INFO', 
      payload: { 
        name: customer.name, 
        phone: customer.phone, 
        address: customer.address 
      } 
    });
  }, [orderState.customer]);

  const handleDeliveryInfoChange = useCallback((deliveryPerson: DeliveryPerson) => {
    dispatch({ 
      type: 'SET_DELIVERY_INFO', 
      payload: deliveryPerson
    });
  }, []);
  
  // --- ACTION DISPATCHERS ---
  const updateSelectedItem = useCallback(async () => {
    if (!uiState.selectedItemForAddons) return;

    // Extract item ID from the format "itemId-size"
    const itemId = uiState.selectedItemForAddons.split('-')[0];
    const size = uiState.selectedItemSizes[itemId] || 'default';
    
    // Find the menu item and its category
    let menuItem: MenuItem | undefined;
    let categoryId: string | undefined;
    
    for (const category of localCategories) {
      const foundItem = category.items.find((item: MenuItem) => item.id === itemId);
      if (foundItem) {
        menuItem = foundItem;
        categoryId = category.id;
        break;
      }
    }
      
    if (!menuItem || !categoryId) return;
    
    // Get selected addons for this item+size combination
    const key = getAddonKey(itemId, size);
    const selectedAddonIds = Array.from(uiState.selectedAddons[key] || new Set());
    
    // Get supplements for this category
    const categorySupplements = await getCategorySupplements(categoryId);
    const supplementPrices = await getCategorySupplementPrices(categoryId);
    
    // Create addon objects from supplements only
    const addonObjects = await Promise.all(
      selectedAddonIds.map(async (addonId: string) => {
        // Check if this addon ID corresponds to a supplement
        const supplement = categorySupplements.find(s => s.id === addonId);
        if (supplement) {
          // Get the correct price for this supplement based on the item's size
          const supplementPrice = supplementPrices[size] || 0;
          return {
            id: supplement.id,
            name: supplement.name,
            price: supplementPrice, // Use category pricing based on item size
            type: 'supplement',
            // Include stock consumption info for backend processing
            stockConsumption: supplement.stockConsumption,
          };
        }
        
        return null;
      })
    );
    
    const validAddonObjects = addonObjects.filter(Boolean) as OrderAddon[];
    
    // Get notes for this item+size combination  
    const noteKey = getItemNoteKey(itemId, size);
    const itemNote = uiState.itemNotes[noteKey] || '';
    
    // Find if this item+size already exists in the order
    const existingItem = orderState.items?.find((item: OrderItem) => 
      item.menuItemId === itemId && item.size === size
    );
    
    // Check if addons or notes have changed
    const addonSignature = validAddonObjects.map((a: any) => a.id).sort().join(',');
    const existingAddonSignature = (existingItem?.addons || [])
      .map((a: any) => a.id).sort().join(',');
    
    const hasChanged = addonSignature !== existingAddonSignature || 
                       existingItem?.notes !== itemNote;
    
    // If the item exists and has changed, update it
    if (existingItem && hasChanged) {
      dispatch({ 
        type: 'UPDATE_ITEM', 
        payload: { 
          itemId: existingItem.id, 
          updates: {
            size: size,
            price: menuItem.prices[size],
            addons: validAddonObjects,
            notes: itemNote
          } 
        } 
      });
    }
  }, [
    uiState.selectedItemForAddons, 
    uiState.selectedItemSizes, 
    uiState.selectedAddons, 
    uiState.itemNotes, 
    orderState.items, 
    localCategories, 
    getAddonKey, 
    getItemNoteKey,
    dispatch,
    getCategorySupplements,
    getCategorySupplementPrices
  ]);
  
  const handleSelectItemForCustomization = useCallback((itemId: string) => {
    // If an item was previously selected, update any changes before selecting the new one
    if (uiState.selectedItemForAddons) {
      updateSelectedItem();
    }

    // Find the item to get its default size
    const item = localCategories
      .flatMap(category => category.items)
      .find((item: MenuItem) => item.id === itemId);

    if (item) {
      const defaultSize = Object.keys(item.prices)[0] || 'default';
      const currentSize = uiState.selectedItemSizes[itemId] || defaultSize;

      // Set selection with size-specific format
      setSelectedItemForAddons(`${itemId}-${currentSize}`);

      // Initialize state for the newly selected item if not already set
      if (!uiState.selectedItemSizes[itemId]) {
        initializeFreshItemState(itemId, currentSize);
      }
    }
  }, [
    uiState.selectedItemForAddons,
    uiState.selectedItemSizes,
    localCategories,
    setSelectedItemForAddons,
    initializeFreshItemState,
    updateSelectedItem
  ]);
  
  const finalizeCurrentItem = useCallback(() => {
    updateSelectedItem();
    setSelectedItemForAddons(null);
  }, [setSelectedItemForAddons, updateSelectedItem]);
  
    // Handle adding menu items to order
  const handleAddItem = useCallback(async (item: MenuItem, size: string) => {
    // Staff menu mode - simplified integration
    if (uiState.selectedCategory === 'staff-menu') {
      // Find the corresponding staff menu item
      const staffMenuItem = staffMenuItems.find(menuItem => {
        const menuItemIdMatch = menuItem.menuItemId === item.id;
        const nameMatch = menuItem.itemName.toLowerCase().trim() === item.name.toLowerCase().trim();
        return menuItemIdMatch || nameMatch;
      });

      // If no specific staff menu item found, create a temporary one from the regular menu item
      let itemToAdd = staffMenuItem;
      if (!staffMenuItem) {
        // Create a temporary staff menu item from the regular menu item
        const price = item.prices[size] || Object.values(item.prices)[0] || 0;
        itemToAdd = {
          id: `temp-${item.id}-${size}`,
          menuItemId: item.id,
          itemName: item.name,
          categoryName: uiState.selectedCategory,
          size: size,
          originalPrice: price,
          staffPrice: Math.floor(price * 0.8), // 20% discount for staff
          isActive: true
        };
        

      }

      // Add item to selected staff via StaffMenuManager
      if (staffMenuRef.current?.addItemToSelectedStaff && isStaffMenuReady) {
        staffMenuRef.current.addItemToSelectedStaff(itemToAdd);
      } else {
        // Retry after a short delay if component is still mounting
        setTimeout(() => {
          if (staffMenuRef.current?.addItemToSelectedStaff) {
            staffMenuRef.current.addItemToSelectedStaff(itemToAdd);
          }
        }, 50);
      }
      return;
    }

    const selectedAddons = uiState.selectedAddons[getAddonKey(item.id, size)] || new Set();
    const itemNotes = uiState.itemNotes[getItemNoteKey(item.id, size)] || '';
    
    // Get supplements for the selected category  
    const categorySupplements = await getCategorySupplements(uiState.selectedCategory);
    const supplementPrices = await getCategorySupplementPrices(uiState.selectedCategory);
    
    // Create addon objects from supplements only
    const addonObjects = await Promise.all(
      Array.from(selectedAddons).map(async (addonId: string) => {
        // Check if this addon ID corresponds to a supplement
        const supplement = categorySupplements.find(s => s.id === addonId);
        if (supplement) {
          // Get the correct price for this supplement based on the item's size
          const supplementPrice = supplementPrices[size] || 0;
          return {
            id: supplement.id,
            name: supplement.name,
            price: supplementPrice,
            type: 'supplement' as const,
            stockConsumption: supplement.stockConsumption,
          };
        }
        
        // If no match found, skip this addon
        return null;
      })
    );

    // Filter out null values and type as OrderAddon[]
    const validAddons = addonObjects.filter(Boolean) as OrderAddon[];
    
    dispatch({ 
      type: 'ADD_ITEM', 
      payload: { 
        item, 
        size, 
        addons: validAddons,
        notes: itemNotes,
        categoryId: uiState.selectedCategory
      } 
    });

    // Create signature for this specific item+size+addons combination
    const signature = `${item.id}-${size}-${Array.from(selectedAddons).sort().join(',')}-${itemNotes}`;
    setLastAddedItem(signature);

    // Clear the addon selection for this item after adding
    setUiState(prev => ({
      ...prev,
      selectedAddons: {
        ...prev.selectedAddons,
        [getAddonKey(item.id, size)]: new Set()
      },
      itemNotes: {
        ...prev.itemNotes,
        [getItemNoteKey(item.id, size)]: ''
      }
    }));

    // 🎯 Open customization panel after adding item to allow addon/notes customization
    setSelectedItemForAddons(`${item.id}-${size}`);
    // Set the selected size for this item so the customization panel shows the right size
    setUiState(prev => ({
      ...prev,
      selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size }
    }));
  }, [uiState.selectedAddons, uiState.itemNotes, uiState.selectedCategory, getAddonKey, getItemNoteKey, getCategorySupplements, getCategorySupplementPrices, setLastAddedItem, toast, dispatch, setUiState, staffMenuItems]);
  
  // 🍕 Handler for inline pizza customizer within addon panel
  const handleInlineCustomPizzaConfirm = useCallback((
    quarters: PizzaQuarter[], 
    size: string, 
    notes: string, 
    categoryId: string, 
    pricingMethod: 'max' | 'average'
  ) => {
    dispatch({
      type: 'ADD_CUSTOM_PIZZA',
      payload: {
        quarters,
        size,
        notes,
        categoryId,
        pricingMethod
      }
    });
    
    // Close the addon panel and reset state
    setSelectedItemForAddons(null);
    
    toast({
      title: "🍕 Pizza personnalisée ajoutée",
      description: `Pizza avec ${quarters.map(q => q.name).join(' + ')} ajoutée à la commande`,
    });
  }, [dispatch, setSelectedItemForAddons, toast]);
  
  const handlePlaceOrder = useCallback(async () => {
    // Validate order
    if (!orderState.items?.length) return;
    if (requiresTable(orderState.orderType) && !orderState.tableId) return;
    if (requiresCustomerInfo(orderState.orderType) &&
        (!orderState.customer?.name || !orderState.customer?.phone)) return;
    
    try {
      setIsOperationLoading(true);
      
        // Regular order handling
        const combinedItemsMap: { [key: string]: OrderItem } = {};
        orderState.items.forEach((item: OrderItem) => {
          const itemSignature = getItemSignature(item);
          if (combinedItemsMap[itemSignature]) {
            combinedItemsMap[itemSignature].quantity += item.quantity;
          } else {
            combinedItemsMap[itemSignature] = { ...item };
          }
        });
        
      const combinedItems = Object.values(combinedItemsMap);
      
      // Create new order object
      const newOrder: NewOrder = {
        tableId: orderState.orderType === 'dine-in' ? orderState.tableId : '',
        orderType: orderState.orderType,
        status: 'pending',
        items: combinedItems.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
        total: calculateTotal(combinedItems),
        notes: orderState.notes,
        customer: requiresCustomerInfo(orderState.orderType)
          ? orderState.customer
          : undefined,
        deliveryPerson: orderState.orderType === 'delivery' 
          ? orderState.deliveryPerson 
          : undefined,
        paymentStatus: 'unpaid',
        createdBy: user?.name || (user as any)?.username || 'unknown',
        createdByName: user?.name || 'Personnel Inconnu'
      };
      
      // Show print preview popup BEFORE creating the order
      const date = new Date();
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const dateStr = `${year}${month}${day}`;
      
      const orderForPreview: Order = {
        ...newOrder,
        _id: `order:${dateStr}-preview`,
        id: `order:${dateStr}-preview`,
        type: 'order_document',
        schemaVersion: 'v4.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // Store the order data for later creation
      (window as any).pendingOrderData = { newOrder, combinedItems };
      
      // Show the all print preview popup
      setShowAllPrintPreview(true);
      
    } catch (error) {
      // Optionally handle error
      toast({
        title: "Error preparing order",
        description: "Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsOperationLoading(false);
    }
  }, [
    orderState, 
    toast,
    dispatch,
    setUiState
  ]);

  // Handle confirming the order after print preview
  const handleConfirmOrder = useCallback(async () => {
    console.log('🎯 [handleConfirmOrder] Starting order confirmation process...');
    try {
      setIsOperationLoading(true);
      
      // Get the stored order data
      const { newOrder, combinedItems } = (window as any).pendingOrderData || {};
      console.log('📋 [handleConfirmOrder] Retrieved pending order data:', { newOrder, combinedItems });
      
      if (!newOrder) {
        throw new Error('No pending order data found');
      }
      
      // Create freelancer if this is a delivery order with freelance driver
      if (newOrder.orderType === 'delivery' && 
          newOrder.deliveryPerson?.type === 'freelance' && 
          (newOrder.deliveryPerson.phone || newOrder.deliveryPerson.name)) {
        
        try {
          console.log('🚚 Creating freelancer for confirmed delivery order:', {
            name: newOrder.deliveryPerson.name,
            phone: newOrder.deliveryPerson.phone
          });
          
          // Import and create the freelancer
          const { createOrGetFreelancer } = await import('@/lib/db/v4/operations/freelancer-ops');
          await createOrGetFreelancer(newOrder.deliveryPerson.name, newOrder.deliveryPerson.phone);
          console.log('✅ Freelancer created/retrieved successfully');
        } catch (freelancerError) {
          console.error('⚠️ Error creating freelancer (continuing with order):', freelancerError);
          // Don't fail the order creation if freelancer creation fails
        }
      }
      
      // Create the order
      console.log('🔄 [handleConfirmOrder] Calling createOrder with data:', newOrder);
      const created = await createOrder(newOrder);
      console.log('✅ [handleConfirmOrder] Order created successfully:', created);
      
      // Staff menu allowance tracking will be handled by StaffMenuManager integration
      
      // Print to kitchen after order is placed - smart printing
      try {
        console.log('🖨️ [handleConfirmOrder] Starting kitchen print process...');
        const orderForPrint: Order = {
          ...newOrder,
          _id: created?._id || created?.id || '',
          id: created?.id || created?._id || '',
          type: 'order_document',
          schemaVersion: 'v4.0',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        const result = await kitchenPrintService.printKitchenOrder(
          orderForPrint,
          orderState.tableId || undefined,
          { fontSize: 'medium' }
        );
        
        // Don't show individual print preview since we already showed all previews
        console.log('🖨️ Kitchen print result:', result);
        
      } catch (error) {
        console.error('❌ Kitchen print error:', error);
      }
      
      // Refresh orders after a delay
      console.log('🔄 [handleConfirmOrder] Refreshing orders...');
      setTimeout(() => { refreshOrders(); }, 1000);
      
      // Reset the order and clear edit mode
      console.log('🔄 [handleConfirmOrder] Resetting order state...');
      dispatch({ type: 'RESET_ORDER' });
      
      // Clear edit mode and UI state
      if (isEditMode) {
        clearEditOrder();
      }
      setUiState(initialUiState);
      
      // Clear staff selections if in staff menu mode
      if (uiState.selectedCategory === 'staff-menu') {
        // Staff menu clearing will be handled by StaffMenuManager integration
      }
      
      // Clean up stored data
      delete (window as any).pendingOrderData;
      
      toast({
        title: "✅ Order placed successfully!",
        description: "Kitchen tickets have been printed.",
        variant: "default"
      });
      
      console.log('✅ [handleConfirmOrder] Order confirmation process completed successfully');
      
    } catch (error) {
      console.error('❌ [handleConfirmOrder] Error creating order:', error);
      console.error('❌ [handleConfirmOrder] Error details:', {
        message: (error as any)?.message,
        stack: (error as any)?.stack,
        name: (error as any)?.name
      });
      toast({
        title: "Error placing order",
        description: `Please try again. Error: ${(error as any)?.message || 'Unknown error'}`,
        variant: "destructive"
      });
    } finally {
      setIsOperationLoading(false);
      console.log('🏁 [handleConfirmOrder] Process finished, loading state cleared');
    }
  }, [
    createOrder, 
    refreshOrders, 
    toast, 
    dispatch,
    setUiState,
    orderState.tableId
  ]);

  // Memoize the refreshOrders button handler 
  const handleRefreshOrders = useCallback(() => {
    refreshOrders();
  }, [refreshOrders]);

  // Memoize the displayCategories value to prevent unnecessary recalculations
  const displayCategories = useMemo(() => {
    const baseCategories = localCategories.length > 0 ? localCategories : categories;
    
    // 🔍 DEBUG: Log staff menu values
    console.log('🔍 [Staff Menu Debug]', {
      staffMenuConfigIsEnabled: staffMenuConfig?.isEnabled,
      staffMenuItemsLength: staffMenuItems.length,
      staffMenuItems: staffMenuItems,
      userRole: user?.role,
      allConditionsMet: staffMenuConfig?.isEnabled && staffMenuItems.length > 0
    });
    
    // Add staff menu category if configured and user is authenticated staff  
    if (staffMenuConfig?.isEnabled && staffMenuItems.length > 0) {
      
      const staffCategory = {
        id: 'staff-menu',
        name: 'Équipe',
        emoji: '👥',
        color: '#10b981', // Green color for staff
        items: staffMenuItems
          .filter(staffItem => staffItem.isActive !== false)
          .map(staffItem => ({
            id: `staff-${staffItem.id}`,
            name: staffItem.itemName,
            prices: { default: staffItem.staffPrice },
            isStaffItem: true,
            originalMenuItemId: staffItem.menuItemId,
            staffItemId: staffItem.id,
            categoryName: staffItem.categoryName,
            size: staffItem.size,
            originalPrice: staffItem.originalPrice
          })),
        isStaffCategory: true
      };
      
      return [...baseCategories, staffCategory];
    }
    
    return baseCategories;
  }, [localCategories, categories, staffMenuConfig, staffMenuItems, user]);

  // --- EFFECTS ---
  // Initialize selected category when categories are loaded
  useEffect(() => {
    if (isReady && categories.length > 0) {
      setSelectedCategory(categories[0]?.id || "");
    }
  }, [isReady, categories, setSelectedCategory]);
  
  // Initialize table statuses when tables and orders are loaded
  useEffect(() => {
    if (isReady && tables.length > 0) {
      const initialTableStatuses = tables.map(table => {
        const activeOrder = orders.find((o: any) => 
          o.tableId === table.id && (o.status === 'pending' || o.status === 'preparing')
        );
        
        return {
          id: table.id || "",
          status: activeOrder ? 'occupied' as const : 'free' as const,
          occupiedSince: activeOrder ? activeOrder.createdAt : null,
          currentOrderId: activeOrder?._id || null
        };
      });
      
      setTableStatuses(initialTableStatuses);
    }
  }, [isReady, tables, orders]);
  
  // Create a memoized handler reference that persists between renders
  const handleClickOutsideRef = useRef<((event: MouseEvent) => void) | null>(null);
  
  // Update the handler reference when finalizeCurrentItem changes
  useEffect(() => {
    handleClickOutsideRef.current = (event: MouseEvent) => {
      const target = event.target as Node;
      
      // Check if click is outside popup
      if (popupRef.current && !popupRef.current.contains(target)) {
        // Also check if click is on a Select dropdown (which is portaled)
        const selectElement = (target as Element).closest('[data-radix-select-content]') || 
                             (target as Element).closest('[data-radix-select-item]') ||
                             (target as Element).closest('[data-radix-popper-content-wrapper]');
        
        // Don't close if clicking on Select dropdown
        if (!selectElement) {
          finalizeCurrentItem();
        }
      }
    };
  }, [finalizeCurrentItem]);

  // Add the event listener only once
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      handleClickOutsideRef.current?.(event);
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  // 🎯 Enhanced edit order handling with state persistence
  useEffect(() => {
    if (editOrder && editSessionId) {
      console.log('📝 [NewOrderingInterface] Loading order for editing:', editOrder.id || editOrder._id);
      
      // Load the order into the reducer
      dispatch({ type: 'LOAD_ORDER', payload: editOrder });
      
      // 🔄 Restore UI state from localStorage if available
      try {
        const storedUiState = localStorage.getItem(`ui_state_${editSessionId}`);
        if (storedUiState) {
          const parsedUiState = JSON.parse(storedUiState);
          console.log('🔄 [NewOrderingInterface] Restoring UI state from localStorage');
          
          // Restore selected addons (convert arrays back to Sets)
          const restoredAddons: { [key: string]: Set<string> } = {};
          Object.entries(parsedUiState.selectedAddons || {}).forEach(([key, value]) => {
            restoredAddons[key] = new Set(value as string[]);
          });
          
          setUiState(prev => ({
            ...prev,
            selectedItemSizes: parsedUiState.selectedItemSizes || {},
            itemNotes: parsedUiState.itemNotes || {},
            selectedAddons: restoredAddons,
            selectedCategory: parsedUiState.selectedCategory || prev.selectedCategory
          }));
        }
      } catch (error) {
        console.warn('⚠️ [NewOrderingInterface] Failed to restore UI state:', error);
      }
    } else if (!editOrder) {
      // Clear UI state when not editing
      setUiState(initialUiState);
    }
  }, [editOrder, editSessionId]);
  
  // 💾 Persist UI state changes when in edit mode
  useEffect(() => {
    if (isEditMode && editSessionId) {
      try {
        // Convert Sets to arrays for JSON serialization
        const serializableAddons: { [key: string]: string[] } = {};
        Object.entries(uiState.selectedAddons).forEach(([key, value]) => {
          serializableAddons[key] = Array.from(value);
        });
        
        const uiStateToStore = {
          selectedItemSizes: uiState.selectedItemSizes,
          itemNotes: uiState.itemNotes,
          selectedAddons: serializableAddons,
          selectedCategory: uiState.selectedCategory
        };
        
        localStorage.setItem(`ui_state_${editSessionId}`, JSON.stringify(uiStateToStore));
      } catch (error) {
        console.warn('⚠️ [NewOrderingInterface] Failed to persist UI state:', error);
      }
    }
  }, [uiState.selectedItemSizes, uiState.itemNotes, uiState.selectedAddons, uiState.selectedCategory, isEditMode, editSessionId]);
  
  // 🧹 Cleanup UI state when edit session ends
  useEffect(() => {
    return () => {
      if (editSessionId) {
        try {
          localStorage.removeItem(`ui_state_${editSessionId}`);
        } catch (error) {
          console.warn('⚠️ [NewOrderingInterface] Failed to cleanup UI state:', error);
        }
      }
    };
  }, [editSessionId]);
  
  // Process categories to ensure they have supplements available
  useEffect(() => {
    if (categories.length > 0) {
      // Categories now use supplements instead of addons
      // No need to add fake addons - supplements are managed separately
      setLocalCategories(categories);
    }
  }, [categories]);
  
  // Additional effect to update item when selected addons or notes change
  useEffect(() => {
    updateSelectedItem();
  }, [uiState.selectedAddons, uiState.itemNotes, updateSelectedItem]);
  
  // --- JSX: MAIN COMPONENT RENDER ---
  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">{error.message}</p>
            <Button className="mt-4" onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Initializing System</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center p-8 gap-4">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
              <p className="text-sm text-muted-foreground">{isAuthenticated ? "Loading data..." : "Connecting to database..."}</p>
              {loadingTimeout && (
                <div className="text-center space-y-2">
                  <p className="text-sm text-orange-600">⚠️ Loading is taking longer than expected</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => window.location.reload()}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Page
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="w-full h-full flex flex-col overflow-hidden p-2">
      <Card className="flex-grow flex flex-col shadow-sm overflow-hidden h-full">
          <div className="grid grid-cols-12 flex-grow gap-0.5 lg:grid-cols-12 md:grid-cols-12 sm:grid-cols-1">
            {/* Left Panel - Menu */}
            <div className="col-span-8 flex flex-col overflow-hidden lg:col-span-8 md:col-span-7 sm:col-span-12 border-r">
              <div className="flex-shrink-0 p-1.5 border-b flex items-center justify-between bg-muted/30">
                <h3 className="font-semibold text-sm">Menu</h3>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="h-7 px-2" onClick={handleRefreshOrders}>
                    <RefreshCw className="h-3.5 w-3.5" />
                  </Button>
                </div>
              </div>
              <div className="flex flex-col flex-grow min-h-0">
                <div className="flex-shrink-0 overflow-auto border-b py-1.5 px-2">
                  <CategorySelector 
                    categories={displayCategories}
                    selectedCategory={uiState.selectedCategory}
                    onSelectCategory={setSelectedCategory}
                  />
                </div>
                <div className="flex-grow overflow-hidden flex flex-col relative">
                  {/* Menu Items Section */}
                  <div className="flex-grow overflow-auto min-h-0">
                    {displayCategories.map((category) => (
                      <div key={category.id || ''} className={`h-full w-full overflow-auto ${uiState.selectedCategory === category.id ? 'block' : 'hidden'}`}>
                        <ScrollArea className="h-full w-full">
                          <div className="p-1.5 grid auto-rows-auto grid-cols-4 2xl:grid-cols-6 xl:grid-cols-5 lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 gap-1.5">
                            {category.items.map((item: MenuItem) => {
                              const selectedSize = uiState.selectedItemSizes[item.id];
                              const isSelected = selectedSize && uiState.selectedItemForAddons === `${item.id}-${selectedSize}`;
                              return (
                                <MenuItemCard
                                  key={item.id}
                                  item={item}
                                  categoryId={category.id}
                                  categories={displayCategories}
                                  isSelected={isSelected}
                                  selectedSize={selectedSize}
                                  lastAddedItem={uiState.lastAddedItem}
                                  onSelect={handleSelectItemForCustomization}
                                  onAddItem={handleAddItem}
                                />
                              );
                            })}
                          </div>
                        </ScrollArea>
                      </div>
                    ))}
                  </div>
                  
                  {/* Item Customization Panel */}
                  {uiState.selectedItemForAddons && (() => {
                    // Extract item ID from the format "itemId-size"
                    const itemId = uiState.selectedItemForAddons.split('-')[0];
                    const category = displayCategories.find((c: any) =>
                      c.items.some((item: MenuItem) => item.id === itemId)
                    );
                    const item = category?.items.find((item: MenuItem) =>
                      item.id === itemId
                    );
                    const selectedSize = uiState.selectedItemSizes[itemId] ||
                      Object.keys(item?.prices || {})[0] || 'default';
                    
                    if (!item || !category) return null;
                    
                    return (
                      <div ref={popupRef}>
                        <ItemCustomizationPanel
                          category={category}
                          item={item}
                          selectedSize={selectedSize}
                          selectedAddons={uiState.selectedAddons}
                          itemNotes={uiState.itemNotes}
                          getAddonKey={getAddonKey}
                          getItemNoteKey={getItemNoteKey}
                          isAddonSelected={isAddonSelected}
                          toggleAddonSelection={toggleAddonSelection}
                          updateItemNote={updateItemNote}
                          finalizeItem={finalizeCurrentItem}
                          categories={displayCategories}
                          onCustomPizzaConfirm={handleInlineCustomPizzaConfirm}
                          onClose={() => {
                            // Find the most recent item and remove it
                            if (orderState.items.length > 0) {
                              const mostRecentItem = orderState.items[orderState.items.length - 1];
                              handleRemoveItem(mostRecentItem.id);
                            }
                            setSelectedItemForAddons(null);
                          }}
                        />
                      </div>
                    );
                  })()}
                  

                </div>
              </div>
            </div>
            
            {/* Right Panel - Order Details & Summary */}
            <div className="col-span-4 flex flex-col overflow-hidden lg:col-span-4 md:col-span-5 sm:col-span-12 pl-0.5 h-full">
              <div className="flex-shrink-0 p-1.5 border-b bg-muted/30">
                <h3 className="font-semibold text-sm">Détails de la commande</h3>
              </div>
              
              {/* Order Type, Table Selection, Customer Info - Hide for staff menu */}
              {uiState.selectedCategory !== 'staff-menu' && (
                <OrderDetails
                  orderType={orderState.orderType}
                  tableId={orderState.tableId}
                  tables={tables}
                  tableStatuses={tableStatuses}
                  customer={orderState.customer}
                  deliveryPerson={orderState.deliveryPerson}
                  notes={orderState.notes}
                  onOrderTypeChange={handleOrderTypeChange}
                  onTableChange={handleTableChange}
                  onNotesChange={handleNotesChange}
                  onCustomerInfoChange={handleCustomerInfoChange}
                  onDeliveryPersonChange={handleDeliveryInfoChange}
                />
              )}
              
              {/* Staff Menu Manager */}
              {uiState.selectedCategory === 'staff-menu' && (
                <StaffMenuManager
                  ref={(ref) => {
                    staffMenuRef.current = ref;
                    setIsStaffMenuReady(!!ref);
                  }}
                  config={staffMenuConfig}
                  menuItems={staffMenuItems}
                  currentUser={user}
                  onStaffSelect={(staffId) => {
                    // Handle staff selection if needed
                  }}
                />
              )}
              
              {/* Order Items and Summary - Hide for staff menu */}
              {uiState.selectedCategory !== 'staff-menu' && (
                <OrderSummary
                  items={orderState.items}
                  total={orderState.total}
                  onIncrement={handleIncrement}
                  onDecrement={handleDecrement}
                  onRemove={handleRemoveItem}
                  selectedItemForAddons={uiState.selectedItemForAddons}
                  selectedItemSizes={uiState.selectedItemSizes}
                  setSelectedItemForAddons={setSelectedItemForAddons}
                  setSelectedItemSizes={(updater) => setUiState(prev => ({
                    ...prev,
                    selectedItemSizes: updater(prev.selectedItemSizes)
                  }))}
                  setSelectedAddons={(updater) => setUiState(prev => ({
                    ...prev,
                    selectedAddons: updater(prev.selectedAddons)
                  }))}
                  setItemNotes={(updater) => setUiState(prev => ({
                    ...prev,
                    itemNotes: updater(prev.itemNotes)
                  }))}
                  getAddonKey={getAddonKey}
                  getItemNoteKey={getItemNoteKey}
                  displayCategories={displayCategories}
                />
              )}
              

              
              {/* Place Order Button - Hide for staff menu */}
              {uiState.selectedCategory !== 'staff-menu' && (
                <div className="flex-shrink-0 border-t mt-auto">
                  <div className="px-3 py-2 flex items-center justify-between border-b bg-muted/10">
                    <h3 className="font-medium text-base">Total</h3>
                    <div className="font-bold text-lg text-primary tabular-nums">{orderState.total} DA</div>
                  </div>
                  <div className="p-2.5">
                    <Button 
                      className="w-full h-11 text-base font-semibold bg-gray-900 hover:bg-black text-white border-gray-900 shadow-sm"
                      size="lg"
                      disabled={
                        isOperationLoading || 
                        !orderState.items.length || 
                        (requiresTable(orderState.orderType) && !orderState.tableId) ||
                        (requiresCustomerInfo(orderState.orderType) &&
                          (!orderState.customer?.name || !orderState.customer?.phone))
                      }
                      onClick={handlePlaceOrder}
                    >
                      {isOperationLoading ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary-foreground border-t-transparent"></div>
                          <span>Traitement...</span>
                        </div>
                      ) : (
                        <>
                          <Receipt className="h-4 w-4 mr-2" />
                          <span>{isEditMode ? "Modifier la commande" : "Passer la commande"}</span>
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
              

            </div>
          </div>
      </Card>
      
      {/* All Print Preview Modal */}
      {showAllPrintPreview && (window as any).pendingOrderData && (
        <AllPrintPreview
          open={showAllPrintPreview}
          onClose={() => {
            setShowAllPrintPreview(false);
            delete (window as any).pendingOrderData;
          }}
          order={{
            ...(window as any).pendingOrderData.newOrder,
            _id: `order:${new Date().toISOString().split('T')[0].replace(/-/g, '')}-preview2`,
            id: `order:${new Date().toISOString().split('T')[0].replace(/-/g, '')}-preview2`,
            type: 'order_document',
            schemaVersion: 'v4.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }}
          tableId={orderState.tableId}
          onConfirm={handleConfirmOrder}
        />
      )}
      
      {/* Print Preview Modal */}
      {showPrintPreview && printJob && (
        <PrintPreviewDialog
          open={showPrintPreview}
          printJob={printJob}
          onOpenChange={setShowPrintPreview} // Use onOpenChange for Shadcn Dialog
          onPrint={() => {}} // Provide an empty function for onPrint
        />
      )}
    </div>
  );
};

export default NewOrderingInterface;